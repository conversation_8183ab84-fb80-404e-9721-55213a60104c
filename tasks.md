# Ensure Show/Hide Options reflect checkbox state on all dashboard charts

## Task Overview
Fix inconsistency where the Show/Hide Options checkboxes (Show Returns, Show Royalties) don’t always affect the chart rendering. Ensure each chart card strictly follows the selected state and persists correctly per chart.

## Implementation Tasks

- [x] Task 1: Locate Show/Hide Options logic in `components/dashboard/dashboard.js` and chart option usage in `components/charts/snap-charts.js`.
- [x] Task 2: Correct toggle state reading to use strict `'true'` checks from `sessionStorage`.
- [x] Task 3: On chart initialization for all four charts, immediately apply persisted `showReturns`/`showRoyalties` to `chart.options` and re-render if needed.
- [ ] Task 4: Verify with test flows (toggle each option on all cards, reload page) to confirm persistence and visual correctness.
- [ ] Task 5: Document behavior and any follow-ups.

## Notes
- Toggle now uses strict read (`=== 'true'`) to avoid default-true mismatch.
- Initial application added to Last Week, Today vs Previous, Monthly, and Yearly chart initializations.


